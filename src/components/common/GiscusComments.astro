---

---
<div id="giscus-container" class="giscus-container">
  <!-- 加载状态指示器 -->
  <div id="giscus-loading" class="giscus-loading">
    <div class="loading-spinner"></div>
    <p>正在加载评论...</p>
  </div>

  <script is:inline>
    // 动态设置正确的初始主题
    const theme = document.documentElement.getAttribute('data-theme');
    const giscusTheme = theme === 'light' ? 'light' : 'dark_dimmed';

    const script = document.createElement('script');
    script.src = 'https://giscus.app/client.js';
    script.setAttribute('data-repo', 'moatkon/moatkon');
    script.setAttribute('data-repo-id', 'R_kgDOO_cyBw');
    script.setAttribute('data-category', 'Announcements');
    script.setAttribute('data-category-id', 'DIC_kwDOO_cyB84CvDL1');
    script.setAttribute('data-mapping', 'pathname');
    script.setAttribute('data-strict', '0');
    script.setAttribute('data-reactions-enabled', '1');
    script.setAttribute('data-emit-metadata', '0');
    script.setAttribute('data-input-position', 'top');
    script.setAttribute('data-theme', giscusTheme);
    script.setAttribute('data-lang', 'zh-CN');
    script.setAttribute('data-loading', 'lazy');
    script.crossOrigin = 'anonymous';
    script.async = true;

    document.getElementById('giscus-container').appendChild(script);
  </script>
</div>

<script>
  function updateGiscusTheme() {
    const iframe = document.querySelector('iframe.giscus-frame') as HTMLIFrameElement;
    if (iframe?.contentWindow) {
      const theme = document.documentElement.getAttribute('data-theme');
      iframe.contentWindow.postMessage({
        giscus: { setConfig: { theme: theme === 'light' ? 'light' : 'dark_dimmed' } }
      }, 'https://giscus.app');
    }
  }

  // 监听giscus加载
  const interval = setInterval(() => {
    if (document.querySelector('iframe.giscus-frame')) {
      clearInterval(interval);
      document.getElementById('giscus-loading')!.style.display = 'none';
      updateGiscusTheme();
    }
  }, 100);

  // 监听主题变化
  new MutationObserver(updateGiscusTheme).observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['data-theme']
  });
</script>

<style>
  .giscus-container {
    margin-top: 2rem;
    margin-bottom: 2rem;
    padding: 1rem 0;
  }

  /* 确保giscus在不同主题下的样式一致性 */
  :global(.giscus) {
    width: 100%;
  }

  /* 移除giscus边框，与网站融合 */
  .giscus-container :global(.giscus-frame) {
    border: none !important;
    background: transparent;
  }

  /* 使用网站的主题机制 - 与其他组件保持一致 */
  :root[data-theme="light"] .giscus-container {
    color-scheme: light;
  }

  :root[data-theme="dark"] .giscus-container {
    color-scheme: dark;
  }

  /* 加载指示器样式 */
  .giscus-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
    color: var(--sl-color-gray-3);
  }

  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--sl-color-gray-5);
    border-top: 3px solid var(--sl-color-accent);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .giscus-loading p {
    margin: 0;
    font-size: 0.9rem;
  }

  /* 配置警告样式 */
  .giscus-config-warning {
    background-color: var(--sl-color-orange-low);
    border: 1px solid var(--sl-color-orange);
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 2rem 0;
    text-align: center;
  }

  .giscus-config-warning p {
    margin: 0.5rem 0;
    color: var(--sl-color-orange-high);
  }

  .giscus-config-warning a {
    color: var(--sl-color-orange-high);
    text-decoration: underline;
  }

  .giscus-config-warning code {
    background-color: var(--sl-color-gray-6);
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-family: var(--sl-font-mono);
    font-size: 0.9em;
  }

  .config-details {
    margin-top: 1rem;
    text-align: left;
  }

  .config-details summary {
    cursor: pointer;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: var(--sl-color-orange-high);
  }

  .config-details ol {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
  }

  .config-details li {
    margin: 0.25rem 0;
    color: var(--sl-color-orange-high);
  }
</style>
